"""
کلاس اصلی ترجمه کلمه به کلمه قرآن
"""

import os
import json
import time
from typing import List, Dict, Any, Optional
from datetime import datetime

# Django imports
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from apps.quran.models import QuranSura, QuranVerse
from .config import TranslationConfig
from .client import GeminiTranslationClient


class QuranWordTranslator:
    """کلاس اصلی ترجمه کلمه به کلمه قرآن"""
    
    def __init__(self, api_keys: List[str] = None, test_mode: bool = False):
        """
        مقداردهی مترجم
        
        Args:
            api_keys: لیست کلیدهای API
            test_mode: حالت تست
        """
        self.config = TranslationConfig
        
        # تنظیم حالت تست
        if test_mode:
            self.config.set_test_mode(True)
        
        # اطمینان از وجود دایرکتوری داده‌ها
        self.config.ensure_data_dir()
        
        # ایجاد کلاینت
        self.client = GeminiTranslationClient(api_keys)
        
        # بارگذاری وضعیت قبلی
        self.progress = self.load_progress()
        self.requests_log = self.load_requests_log()
        
        # آمار کلی
        self.total_words = 0
        self.processed_words = 0
        
    def load_progress(self) -> Dict:
        """بارگذاری پیشرفت از فایل"""
        if os.path.exists(self.config.PROGRESS_FILE):
            try:
                with open(self.config.PROGRESS_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ خطا در بارگذاری پیشرفت: {e}")
        
        return {
            "last_processed_index": -1,
            "total_processed": 0,
            "successful_translations": 0,
            "failed_translations": 0,
            "start_time": datetime.now().isoformat(),
            "last_update": datetime.now().isoformat(),
            "test_mode": self.config.TEST_MODE
        }
    
    def save_progress(self):
        """ذخیره پیشرفت در فایل"""
        self.progress["last_update"] = datetime.now().isoformat()
        try:
            with open(self.config.PROGRESS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ خطا در ذخیره پیشرفت: {e}")
    
    def load_requests_log(self) -> List[Dict]:
        """بارگذاری لاگ درخواست‌ها"""
        if os.path.exists(self.config.REQUESTS_LOG_FILE):
            try:
                with open(self.config.REQUESTS_LOG_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ خطا در بارگذاری لاگ: {e}")
        return []
    
    def save_request_log(self, request_data: Dict):
        """ذخیره لاگ درخواست"""
        self.requests_log.append(request_data)
        try:
            with open(self.config.REQUESTS_LOG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.requests_log, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ خطا در ذخیره لاگ: {e}")
    
    def load_main_json(self) -> Dict:
        """بارگذاری فایل main.json"""
        if not os.path.exists(self.config.INPUT_FILE):
            raise FileNotFoundError(f"فایل {self.config.INPUT_FILE} یافت نشد")
        
        with open(self.config.INPUT_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def save_output(self, translated_data: Dict):
        """ذخیره خروجی نهایی"""
        try:
            with open(self.config.OUTPUT_FILE, 'w', encoding='utf-8') as f:
                json.dump(translated_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ خطا در ذخیره خروجی: {e}")
    
    def get_verse_text(self, surah_number: int, ayah_number: int) -> str:
        """دریافت متن کامل آیه از دیتابیس"""
        try:
            if self.config.TEST_MODE:
                # در حالت تست، متن ساختگی برمی‌گردانیم
                return f"متن آیه {surah_number}:{ayah_number} (تست)"
            
            sura = QuranSura.objects.get(index=surah_number)
            verse = QuranVerse.objects.get(sura=sura, number_in_surah=ayah_number)
            return verse.text
        except Exception as e:
            print(f"⚠️ خطا در دریافت متن آیه {surah_number}:{ayah_number}: {e}")
            return ""
    
    def detailed_correct_arabic_text(self, arabic_text: str) -> str:
        """تصحیح متن عربی"""
        if not arabic_text:
            return ""
        
        corrected_text = arabic_text.replace('ٱ', 'ا')
        corrected_text = corrected_text.replace('ۡ', '')  # حذف علامت سکون
        corrected_text = corrected_text.replace('ٱل', 'ال')  # حذف همزه اضافی
        return corrected_text.strip()
    
    def translate_batch(self, words_batch: List[Dict]) -> List[str]:
        """ترجمه یک دسته از کلمات"""
        if not words_batch:
            return []
        
        # دریافت متن آیه
        first_word = words_batch[0]
        verse_text = self.get_verse_text(first_word['surah_number'], first_word['ayah_number'])
        
        # ترجمه با کلاینت
        translations, success, error_msg = self.client.translate_words_batch(words_batch, verse_text)
        
        # ذخیره لاگ درخواست
        request_log = {
            "timestamp": datetime.now().isoformat(),
            "surah": first_word['surah_number'],
            "ayah": first_word['ayah_number'],
            "words_count": len(words_batch),
            "api_key_index": self.client.current_key_index,
            "success": success,
            "words": [w['arabic'] for w in words_batch],
            "translations": translations if success else [],
            "error": error_msg if not success else ""
        }
        self.save_request_log(request_log)
        
        return translations
    
    def run_translation(self, max_words: Optional[int] = None) -> Dict:
        """
        اجرای فرآیند ترجمه
        
        Args:
            max_words: حداکثر تعداد کلمات برای ترجمه (برای تست)
            
        Returns:
            آمار نهایی
        """
        print("🚀 شروع ترجمه کلمه به کلمه قرآن کریم")
        print(f"🔧 حالت: {'تست' if self.config.TEST_MODE else 'عادی'}")
        print(f"📊 تعداد کلیدهای API: {len(self.client.api_keys)}")
        print(f"📦 اندازه دسته: {self.config.BATCH_SIZE}")
        print(f"📁 فایل خروجی: {self.config.OUTPUT_FILE}")
        print("-" * 60)
        
        # تست کلیدهای API
        api_test_results = self.client.test_all_api_keys()
        valid_keys = sum(1 for valid in api_test_results.values() if valid)
        
        if valid_keys == 0:
            print("❌ هیچ کلید API معتبری یافت نشد")
            return {"error": "No valid API keys"}
        
        print(f"✅ {valid_keys} کلید API معتبر یافت شد")
        
        # بارگذاری داده‌ها
        try:
            main_data = self.load_main_json()
            verses = main_data['verses']
            
            # در حالت تست، تعداد کلمات را محدود می‌کنیم
            if self.config.TEST_MODE:
                max_words = max_words or self.config.TEST_MAX_WORDS
                verses = verses[:max_words]
                print(f"🧪 حالت تست: محدود به {len(verses)} کلمه")
            
            self.total_words = len(verses)
            print(f"📖 تعداد کل کلمات: {self.total_words:,}")
            
        except Exception as e:
            print(f"❌ خطا در بارگذاری داده‌ها: {e}")
            return {"error": str(e)}
        
        # ایجاد ساختار خروجی
        output_data = {"verses": []}
        
        # شروع از جایی که قبلاً متوقف شده
        start_index = self.progress["last_processed_index"] + 1
        print(f"🔄 شروع از ایندکس: {start_index}")
        
        # پردازش به صورت دسته‌ای
        current_batch = []
        batch_start_index = start_index
        
        try:
            for i in range(start_index, self.total_words):
                verse_data = verses[i]
                
                # اضافه کردن به دسته فعلی
                current_batch.append({
                    'index': i,
                    'surah_number': verse_data['surah_number'],
                    'ayah_number': verse_data['ayah_number'],
                    'arabic': verse_data['arabic'],
                    'english': verse_data['english'],
                    'ayah_words': verse_data['ayah_words'],
                    'order': verse_data['order']
                })
                
                # اگر دسته پر شد یا به آخر رسیدیم
                batch_size = self.config.TEST_BATCH_SIZE if self.config.TEST_MODE else self.config.BATCH_SIZE
                
                if len(current_batch) >= batch_size or i == self.total_words - 1:
                    print(f"\n🔄 پردازش دسته {batch_start_index}-{i} ({len(current_batch)} کلمه)")
                    
                    # ترجمه دسته
                    translations = self.translate_batch(current_batch)
                    
                    # اضافه کردن نتایج به خروجی
                    for j, word_data in enumerate(current_batch):
                        translation = translations[j] if j < len(translations) else ""
                        
                        # تصحیح متن عربی
                        clean_arabic = self.detailed_correct_arabic_text(word_data['arabic'])
                        
                        # ایجاد رکورد خروجی
                        output_record = {
                            "surah_number": word_data['surah_number'],
                            "ayah_number": word_data['ayah_number'],
                            "arabic": clean_arabic,
                            "azerbaijani": translation,
                            "ayah_words": word_data['ayah_words'],
                            "order": word_data['order']
                        }
                        
                        output_data["verses"].append(output_record)
                        
                        # به‌روزرسانی آمار
                        if translation and translation.strip():
                            self.progress["successful_translations"] += 1
                        else:
                            self.progress["failed_translations"] += 1
                    
                    # به‌روزرسانی پیشرفت
                    self.progress["last_processed_index"] = i
                    self.progress["total_processed"] = i + 1
                    self.save_progress()
                    
                    # ذخیره خروجی موقت
                    self.save_output(output_data)
                    
                    # نمایش پیشرفت
                    progress_percent = ((i + 1) / self.total_words) * 100
                    print(f"✅ پیشرفت: {progress_percent:.1f}% ({i + 1:,}/{self.total_words:,})")
                    print(f"📊 موفق: {self.progress['successful_translations']:,}, ناموفق: {self.progress['failed_translations']:,}")
                    
                    # ریست دسته
                    current_batch = []
                    batch_start_index = i + 1
                    
                    # تأخیر بین درخواست‌ها (فقط در حالت عادی)
                    if not self.config.TEST_MODE and i < self.total_words - 1:
                        print(f"⏱️ تأخیر {self.config.BATCH_DELAY} ثانیه...")
                        time.sleep(self.config.BATCH_DELAY)
        
        except KeyboardInterrupt:
            print("\n⚠️ ترجمه توسط کاربر متوقف شد")
            self.save_progress()
            self.save_output(output_data)
        
        # آمار نهایی
        final_stats = {
            "total_words": self.total_words,
            "processed_words": self.progress["total_processed"],
            "successful_translations": self.progress["successful_translations"],
            "failed_translations": self.progress["failed_translations"],
            "client_stats": self.client.get_stats(),
            "test_mode": self.config.TEST_MODE,
            "output_file": self.config.OUTPUT_FILE
        }
        
        return final_stats
