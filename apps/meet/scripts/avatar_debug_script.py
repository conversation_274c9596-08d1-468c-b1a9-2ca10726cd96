#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Avatar Debug Script for Meet App
This script tests the avatar upload process and provider profile creation for Meet app
"""

import os
import json
import time
import random
import shutil
import requests
from datetime import datetime


class AvatarDebugScript:
    def __init__(self):
        # API Configuration
        self.base_url = "https://habibapp.com"
        self.upload_url = f"{self.base_url}/upload-tmp-media/"
        self.profile_url = f"{self.base_url}/habibmeet/meets/provider/profile/"
        self.token = "45361937ef716c7613c6ff4b13ff2ddc784bd66e"
        
        # Setup session with headers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'dart:io',
            'Authorization': f'Token {self.token}',
            'Accept': 'application/json',
        })
        
        # Original image file path
        self.original_image_path = "/home/<USER>/Desktop/main/habbib/habib/backend/apps/meet/scripts/habib_logo.png"
        self.temp_image_dir = "/tmp/avatar_debug"
        self.results_file = "avatar_debug_results.json"
        
        # Create temporary directory
        os.makedirs(self.temp_image_dir, exist_ok=True)
        
        # List of Persian names for images (slugified)
        self.persian_names = [
            "ali-reza", "mohammad-hossein", "fatemeh-zahra", "maryam-sadat", "hasan-ali",
            "zeinab-kobra", "mohammad-baqer", "ali-akbar", "fatemeh-masumeh", "hossein-ali",
            "zahra-batool", "mohammad-reza", "ali-mohammad", "khadijeh-kobra", "hasan-mojtaba",
            "roghayeh-khatoon", "mohammad-taqi", "ali-naqi", "fatemeh-roghayeh", "hossein-zeinolabeddin"
        ]
        
        # Sample profile data
        self.sample_profiles = [
            {
                "full_name": "Dr. Ali Rezaei",
                "bio": "Quran Sciences and Tafsir Specialist",
                "email": "<EMAIL>",
                "institution_name": "Qom University",
                "wa_number": "989123456789"
            },
            {
                "full_name": "Professor Mohammad Hosseini",
                "bio": "Islamic Ethics and Mysticism Teacher",
                "email": "<EMAIL>", 
                "institution_name": "Najaf Seminary",
                "wa_number": "989987654321"
            },
            {
                "full_name": "Dr. Fatemeh Ahmadi",
                "bio": "Islamic History Researcher",
                "email": "<EMAIL>",
                "institution_name": "Tehran University",
                "wa_number": "989111222333"
            }
        ]
        
        # Load previous results if exists
        self.results = self.load_results()

    def load_results(self):
        """Load previous results from JSON file"""
        if os.path.exists(self.results_file):
            try:
                with open(self.results_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return []
        return []

    def save_results(self):
        """Save results to JSON file"""
        with open(self.results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

    def generate_random_image_name(self):
        """Generate random Persian name for image"""
        base_name = random.choice(self.persian_names)
        random_suffix = random.randint(1000, 9999)
        return f"{base_name}-{random_suffix}.png"

    def create_temp_image(self, new_name):
        """Create temporary copy of image with new name"""
        temp_path = os.path.join(self.temp_image_dir, new_name)
        # Copy original file to temp path with new name
        shutil.copy2(self.original_image_path, temp_path)
        return temp_path
    
    def rename_image_file(self, current_path, new_name):
        """Rename image file using os.rename"""
        directory = os.path.dirname(current_path)
        new_path = os.path.join(directory, new_name)
        
        # Rename file
        os.rename(current_path, new_path)
        return new_path

    def upload_image(self, image_path):
        """Upload image to server"""
        try:
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/png')}
                response = self.session.post(self.upload_url, files=files)
                
            if response.status_code == 200:
                data = response.json()
                # API returns 'path' field with full URL, but we need the relative path for FileFieldSerializer
                file_path = data.get('path', '') or data.get('file', '')
                if not file_path:
                    return {
                        'success': False,
                        'error': f"Neither 'path' nor 'file' field found in response: {data}",
                        'response': data
                    }

                # Extract relative path from full URL for FileFieldSerializer
                # FileFieldSerializer expects path like "/tmp/xxxxx-filename.ext"
                if file_path.startswith('https://habibapp.com/static'):
                    # Extract the /tmp/ part from the URL
                    relative_path = file_path.replace('https://habibapp.com/static', '')
                    if relative_path.startswith('/tmp/'):
                        file_path = relative_path

                return {
                    'success': True,
                    'path': file_path,
                    'url': data.get('path', ''),  # Keep original URL for reference
                    'response': data
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'response': response.text
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response': None
            }

    def get_random_profile_data(self):
        """Generate random profile data"""
        profile = random.choice(self.sample_profiles).copy()
        
        # Add random birthdate
        year = random.randint(1970, 1990)
        month = random.randint(1, 12)
        day = random.randint(1, 28)
        profile['birthdate'] = f"{year}-{month:02d}-{day:02d}"
        
        # Add social media links
        profile['social_medias'] = {
            "facebook": f"https://facebook.com/{random.randint(100000, 999999)}",
            "x": f"https://x.com/user{random.randint(1000, 9999)}",
            "instagram": f"https://instagram.com/user{random.randint(1000, 9999)}",
            "youtube": f"https://youtube.com/c/channel{random.randint(100, 999)}"
        }
        
        # Add languages (default)
        available_languages = ['fa', 'ar', 'en', 'ur', 'tr']
        profile['languages'] = random.sample(available_languages, min(2, len(available_languages)))
            
        return profile

    def create_provider_profile(self, avatar_path, profile_data):
        """Create provider profile"""
        try:
            payload = profile_data.copy()
            payload['avatar'] = avatar_path
            
            response = self.session.post(self.profile_url, json=payload)
            
            if response.status_code in [200, 201]:
                return {
                    'success': True,
                    'response': response.json(),
                    'status_code': response.status_code
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'response': response.text,
                    'status_code': response.status_code
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response': None,
                'status_code': None
            }

    def cleanup_via_api(self):
        """Cleanup records via API (optional)"""
        try:
            # Can add dedicated cleanup API if needed
            # Currently just returns success message
            return {
                'success': True,
                'message': 'Cleanup via API not implemented (needs dedicated cleanup API)'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def save_test_result(self, image_name, upload_result, profile_result, profile_data, cleanup_result, user_id):
        """Save test result"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'image_name': image_name,
            'upload_result': upload_result,
            'profile_result': profile_result,
            'profile_data': profile_data,
            'cleanup_result': cleanup_result,
            'user_id': user_id
        }
        
        self.results.append(result)
        self.save_results()
        return result

    def run_single_test(self):
        """Run a complete test"""
        print("🚀 Starting new test...")
        
        # Generate random name for image
        image_name = self.generate_random_image_name()
        print(f"📸 Image name: {image_name}")
        
        # Create temporary copy of image with new name
        temp_image_path = self.create_temp_image(image_name)
        
        # Upload image
        print("⬆️ Uploading image...")
        upload_result = self.upload_image(temp_image_path)
        
        if not upload_result['success']:
            print(f"❌ Error uploading image: {upload_result['error']}")
            # Clean up temp file on error
            try:
                os.remove(temp_image_path)
            except:
                pass
            return self.save_test_result(image_name, upload_result, None, None, None, None)
        
        print(f"✅ Image uploaded successfully: {upload_result['path']}")
        
        # Generate profile data
        profile_data = self.get_random_profile_data()
        print(f"👤 Profile data: {profile_data['full_name']}")
        
        # Create profile
        print("📝 Creating profile...")
        profile_result = self.create_provider_profile(upload_result['path'], profile_data)
        
        # Cleanup records (optional)
        print("🧹 Cleaning up records...")
        cleanup_result = self.cleanup_via_api()
        if cleanup_result['success']:
            print(f"✅ {cleanup_result.get('message', 'Cleanup completed')}")
        else:
            print(f"❌ Error in cleanup: {cleanup_result['error']}")
        
        # Clean up temp file
        try:
            os.remove(temp_image_path)
            print("🗑️ Temp file cleaned up")
        except Exception as e:
            print(f"⚠️ Error cleaning temp file: {e}")
        
        # Save result
        result = self.save_test_result(
            image_name, 
            upload_result, 
            profile_result, 
            profile_data, 
            cleanup_result,
            None  # user_id
        )
        
        if profile_result['success']:
            print("✅ Test completed successfully")
        else:
            print(f"❌ Error creating profile: {profile_result['error']}")
        
        return result

    def run_continuous_tests(self, count=None, delay=5):
        """Run continuous tests with file renaming in each test"""
        print(f"🔄 Starting continuous tests...")
        print(f"⏱️ Delay between tests: {delay} seconds")

        # Create initial copy of original image
        current_image_name = self.generate_random_image_name()
        current_image_path = self.create_temp_image(current_image_name)

        test_count = 0
        try:
            while count is None or test_count < count:
                test_count += 1
                print(f"\n{'='*50}")
                print(f"🔢 Test number {test_count}")
                print(f"{'='*50}")

                # Generate new name for this test
                new_image_name = self.generate_random_image_name()
                print(f"🔄 Renaming file from {os.path.basename(current_image_path)} to {new_image_name}")

                # Rename file
                try:
                    current_image_path = self.rename_image_file(current_image_path, new_image_name)
                    print(f"✅ File renamed: {new_image_name}")
                except Exception as e:
                    print(f"❌ Error renaming file: {e}")
                    # On error, create new file
                    current_image_path = self.create_temp_image(new_image_name)

                # Run test with new file
                self.run_single_test_with_file(current_image_path, new_image_name)

                if count is None or test_count < count:
                    print(f"⏳ Waiting {delay} seconds...")
                    time.sleep(delay)

        except KeyboardInterrupt:
            print(f"\n⏹️ Tests stopped by user")
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
        finally:
            # Clean up final file
            try:
                if os.path.exists(current_image_path):
                    os.remove(current_image_path)
                    print("🗑️ Final file cleaned up")
            except:
                pass

        print(f"\n📊 Total tests completed: {test_count}")
        self.print_summary()

    def run_single_test_with_file(self, image_path, image_name):
        """Run test with specific file"""
        print(f"📸 Using image: {image_name}")

        # Upload image
        print("⬆️ Uploading image...")
        upload_result = self.upload_image(image_path)

        if not upload_result['success']:
            print(f"❌ Error uploading image: {upload_result['error']}")
            return self.save_test_result(image_name, upload_result, None, None, None, None)

        print(f"✅ Image uploaded successfully: {upload_result['path']}")

        # Generate profile data
        profile_data = self.get_random_profile_data()
        print(f"👤 Profile data: {profile_data['full_name']}")

        # Create profile
        print("📝 Creating profile...")
        profile_result = self.create_provider_profile(upload_result['path'], profile_data)

        # Cleanup records (optional)
        print("🧹 Cleaning up records...")
        cleanup_result = self.cleanup_via_api()
        if cleanup_result['success']:
            print(f"✅ {cleanup_result.get('message', 'Cleanup completed')}")
        else:
            print(f"❌ Error in cleanup: {cleanup_result['error']}")

        # Save result
        result = self.save_test_result(
            image_name,
            upload_result,
            profile_result,
            profile_data,
            cleanup_result,
            None  # user_id
        )

        if profile_result['success']:
            print("✅ Test completed successfully")
        else:
            print(f"❌ Error creating profile: {profile_result['error']}")

        return result

    def print_summary(self):
        """Print test results summary"""
        if not self.results:
            print("📊 No test results available")
            return

        total_tests = len(self.results)
        successful_uploads = sum(1 for r in self.results if r.get('upload_result', {}).get('success', False))
        successful_profiles = sum(1 for r in self.results if r.get('profile_result', {}).get('success', False))

        print(f"\n📊 Test Summary:")
        print(f"   Total tests: {total_tests}")
        print(f"   Successful uploads: {successful_uploads}/{total_tests}")
        print(f"   Successful profiles: {successful_profiles}/{total_tests}")
        print(f"   Upload success rate: {(successful_uploads/total_tests)*100:.1f}%")
        print(f"   Profile success rate: {(successful_profiles/total_tests)*100:.1f}%")

        # Show recent errors
        recent_errors = []
        for result in self.results[-5:]:  # Last 5 results
            upload_result = result.get('upload_result') or {}
            profile_result = result.get('profile_result') or {}

            if not upload_result.get('success', False):
                recent_errors.append(f"Upload: {upload_result.get('error', 'Unknown')}")
            elif not profile_result.get('success', False):
                recent_errors.append(f"Profile: {profile_result.get('error', 'Unknown')}")

        if recent_errors:
            print(f"\n🚨 Recent errors:")
            for error in recent_errors:
                print(f"   - {error}")

    def main_menu(self):
        """Main interactive menu"""
        while True:
            print(f"\n🎯 Meet App Avatar Debug Script")
            print("=" * 50)
            print()
            print("Available options:")
            print("1. Run single test")
            print("2. Run continuous tests")
            print("3. Show results summary")
            print("4. Exit")
            print()

            try:
                choice = input("Choose option (1-4): ").strip()

                if choice == '1':
                    self.run_single_test()
                elif choice == '2':
                    try:
                        count_input = input("Number of tests (press Enter for infinite): ").strip()
                        count = int(count_input) if count_input else None
                        delay_input = input("Delay between tests in seconds (default: 5): ").strip()
                        delay = int(delay_input) if delay_input else 5
                        self.run_continuous_tests(count, delay)
                    except ValueError:
                        print("❌ Invalid input. Using default values.")
                        self.run_continuous_tests()
                elif choice == '3':
                    self.print_summary()
                elif choice == '4':
                    print("👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid choice. Please select 1-4.")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")


if __name__ == "__main__":
    script = AvatarDebugScript()
    script.main_menu()
