#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت دیباگ فرایند آواتار برای اپ میت
این اسکریپت فرایند آپلود آواتار و ایجاد پروفایل ارائه‌دهنده میت را تست می‌کند
"""

import os
import json
import time
import random
import shutil
import requests
from datetime import datetime

class AvatarDebugScript:
    def __init__(self):
        self.base_url = "https://habibapp.com"
        self.upload_url = f"{self.base_url}/upload-tmp-media/"
        self.profile_url = f"{self.base_url}/habibmeet/meets/provider/profile/"
        self.token = "45361937ef716c7613c6ff4b13ff2ddc784bd66e"
        self.user_agent = "dart:io"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.user_agent,
            'Authorization': f'Token {self.token}'
        })
        
        # مسیر فایل تصویر اصلی
        self.original_image_path = "/home/<USER>/Desktop/main/habbib/habib/backend/apps/meet/scripts/habib_logo.png"
        self.temp_image_dir = "/tmp/avatar_debug"
        self.results_file = "avatar_debug_results.json"
        
        # ایجاد دایرکتوری موقت
        os.makedirs(self.temp_image_dir, exist_ok=True)
        
        # لیست نام‌های فارسی برای تصاویر
        self.persian_names = [
            "علی-رضا", "محمد-حسین", "فاطمه-زهرا", "مریم-سادات", "حسن-علی",
            "زینب-کبری", "محمد-باقر", "علی-اکبر", "فاطمه-معصومه", "حسین-علی",
            "زهرا-بتول", "محمد-رضا", "علی-محمد", "خدیجه-کبری", "حسن-مجتبی",
            "رقیه-خاتون", "محمد-تقی", "علی-نقی", "فاطمه-رقیه", "حسین-زین‌العابدین"
        ]
        
        # داده‌های نمونه برای پروفایل
        self.sample_profiles = [
            {
                "full_name": "دکتر علی رضایی",
                "bio": "متخصص علوم قرآنی و تفسیر",
                "email": "<EMAIL>",
                "institution_name": "دانشگاه قم",
                "wa_number": "989123456789"
            },
            {
                "full_name": "استاد محمد حسینی",
                "bio": "مدرس اخلاق و عرفان اسلامی",
                "email": "<EMAIL>", 
                "institution_name": "حوزه علمیه نجف",
                "wa_number": "989987654321"
            },
            {
                "full_name": "دکتر فاطمه احمدی",
                "bio": "پژوهشگر تاریخ اسلام",
                "email": "<EMAIL>",
                "institution_name": "دانشگاه تهران",
                "wa_number": "989111222333"
            }
        ]
        
        # بارگذاری نتایج قبلی اگر وجود دارد
        self.results = self.load_results()

    def load_results(self):
        """بارگذاری نتایج قبلی از فایل JSON"""
        if os.path.exists(self.results_file):
            try:
                with open(self.results_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return []
        return []

    def save_results(self):
        """ذخیره نتایج در فایل JSON"""
        with open(self.results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

    def generate_random_image_name(self):
        """تولید نام تصادفی فارسی برای تصویر"""
        base_name = random.choice(self.persian_names)
        random_suffix = random.randint(1000, 9999)
        return f"{base_name}-{random_suffix}.png"

    def create_temp_image(self, new_name):
        """ایجاد کپی موقت از تصویر با نام جدید"""
        temp_path = os.path.join(self.temp_image_dir, new_name)
        # کپی فایل اصلی به مسیر موقت با نام جدید
        shutil.copy2(self.original_image_path, temp_path)
        return temp_path

    def rename_image_file(self, current_path, new_name):
        """تغییر نام فایل تصویر با استفاده از os.rename"""
        directory = os.path.dirname(current_path)
        new_path = os.path.join(directory, new_name)

        # تغییر نام فایل
        os.rename(current_path, new_path)
        return new_path

    def upload_image(self, image_path):
        """آپلود تصویر به سرور"""
        try:
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/png')}
                response = self.session.post(self.upload_url, files=files)
                
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'path': data.get('file', ''),
                    'url': data.get('url', ''),
                    'response': data
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'response': response.text
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response': None
            }

    def get_random_profile_data(self):
        """تولید داده‌های تصادفی برای پروفایل"""
        profile = random.choice(self.sample_profiles).copy()
        
        # اضافه کردن تاریخ تولد تصادفی
        year = random.randint(1970, 1990)
        month = random.randint(1, 12)
        day = random.randint(1, 28)
        profile['birthdate'] = f"{year}-{month:02d}-{day:02d}"
        
        # اضافه کردن شبکه‌های اجتماعی
        profile['social_medias'] = {
            "facebook": f"https://facebook.com/{random.randint(100000, 999999)}",
            "x": f"https://x.com/user{random.randint(1000, 9999)}",
            "instagram": f"https://instagram.com/user{random.randint(1000, 9999)}",
            "youtube": f"https://youtube.com/c/channel{random.randint(100, 999)}"
        }
        
        # اضافه کردن زبان‌ها (پیش‌فرض)
        available_languages = ['fa', 'ar', 'en', 'ur', 'tr']
        profile['languages'] = random.sample(available_languages, min(2, len(available_languages)))
            
        return profile

    def create_provider_profile(self, avatar_path, profile_data):
        """ایجاد پروفایل ارائه‌دهنده"""
        try:
            payload = profile_data.copy()
            payload['avatar'] = avatar_path
            
            response = self.session.post(self.profile_url, json=payload)
            
            if response.status_code in [200, 201]:
                return {
                    'success': True,
                    'response': response.json(),
                    'status_code': response.status_code
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'response': response.text,
                    'status_code': response.status_code
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response': None,
                'status_code': None
            }

    def cleanup_via_api(self):
        """پاک‌سازی رکوردها از طریق API (اختیاری)"""
        try:
            # در صورت نیاز می‌توان API مخصوص پاک‌سازی اضافه کرد
            # فعلاً فقط پیام موفقیت برمی‌گرداند
            return {
                'success': True,
                'message': 'پاک‌سازی از طریق API انجام نشد (نیاز به پیاده‌سازی API مخصوص)'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def run_single_test(self):
        """اجرای یک تست کامل"""
        print("🚀 شروع تست جدید...")

        # تولید نام تصادفی برای تصویر
        image_name = self.generate_random_image_name()
        print(f"📸 نام تصویر: {image_name}")

        # ایجاد کپی موقت از تصویر با نام جدید
        temp_image_path = self.create_temp_image(image_name)

        # آپلود تصویر
        print("⬆️ آپلود تصویر...")
        upload_result = self.upload_image(temp_image_path)

        if not upload_result['success']:
            print(f"❌ خطا در آپلود تصویر: {upload_result['error']}")
            # پاک کردن فایل موقت در صورت خطا
            try:
                os.remove(temp_image_path)
            except:
                pass
            return self.save_test_result(image_name, upload_result, None, None, None, None)

        print(f"✅ تصویر با موفقیت آپلود شد: {upload_result['path']}")

        # تولید داده‌های پروفایل
        profile_data = self.get_random_profile_data()
        print(f"👤 داده‌های پروفایل: {profile_data['full_name']}")

        # ایجاد پروفایل
        print("📝 ایجاد پروفایل...")
        profile_result = self.create_provider_profile(upload_result['path'], profile_data)

        # پاک‌سازی رکوردها (اختیاری)
        print("🧹 پاک‌سازی رکوردها...")
        cleanup_result = self.cleanup_via_api()
        if cleanup_result['success']:
            print(f"✅ {cleanup_result.get('message', 'پاک‌سازی انجام شد')}")
        else:
            print(f"❌ خطا در پاک‌سازی: {cleanup_result['error']}")

        # پاک کردن فایل موقت
        try:
            os.remove(temp_image_path)
            print("🗑️ فایل موقت پاک شد")
        except Exception as e:
            print(f"⚠️ خطا در پاک کردن فایل موقت: {e}")

        # ذخیره نتیجه
        result = self.save_test_result(
            image_name,
            upload_result,
            profile_result,
            profile_data,
            cleanup_result,
            None  # user_id
        )

        if profile_result['success']:
            print("✅ تست با موفقیت انجام شد")
        else:
            print(f"❌ خطا در ایجاد پروفایل: {profile_result['error']}")

        return result

    def save_test_result(self, image_name, upload_result, profile_result, profile_data, cleanup_result, user_id):
        """ذخیره نتیجه تست"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'image_name': image_name,
            'user_id': user_id,
            'upload_result': upload_result,
            'profile_data': profile_data,
            'profile_result': profile_result,
            'cleanup_result': cleanup_result
        }

        self.results.append(result)
        self.save_results()
        return result

    def run_continuous_tests(self, count=None, delay=5):
        """اجرای تست‌های مداوم با تغییر نام فایل در هر تست"""
        print(f"🔄 شروع تست‌های مداوم...")
        print(f"⏱️ تاخیر بین تست‌ها: {delay} ثانیه")

        # ایجاد یک کپی اولیه از تصویر اصلی
        current_image_name = self.generate_random_image_name()
        current_image_path = self.create_temp_image(current_image_name)

        test_count = 0
        try:
            while count is None or test_count < count:
                test_count += 1
                print(f"\n{'='*50}")
                print(f"🔢 تست شماره {test_count}")
                print(f"{'='*50}")

                # تولید نام جدید برای این تست
                new_image_name = self.generate_random_image_name()
                print(f"🔄 تغییر نام فایل از {os.path.basename(current_image_path)} به {new_image_name}")

                # تغییر نام فایل
                try:
                    current_image_path = self.rename_image_file(current_image_path, new_image_name)
                    print(f"✅ نام فایل تغییر کرد: {new_image_name}")
                except Exception as e:
                    print(f"❌ خطا در تغییر نام فایل: {e}")
                    # در صورت خطا، فایل جدید ایجاد کن
                    current_image_path = self.create_temp_image(new_image_name)

                # اجرای تست با فایل جدید
                self.run_single_test_with_file(current_image_path, new_image_name)

                if count is None or test_count < count:
                    print(f"⏳ انتظار {delay} ثانیه...")
                    time.sleep(delay)

        except KeyboardInterrupt:
            print(f"\n⏹️ تست‌ها توسط کاربر متوقف شد")
        except Exception as e:
            print(f"\n❌ خطای غیرمنتظره: {str(e)}")
        finally:
            # پاک کردن فایل نهایی
            try:
                if os.path.exists(current_image_path):
                    os.remove(current_image_path)
                    print("🗑️ فایل نهایی پاک شد")
            except:
                pass

        print(f"\n📊 مجموع تست‌های انجام شده: {test_count}")
        self.print_summary()

    def run_single_test_with_file(self, image_path, image_name):
        """اجرای تست با فایل مشخص"""
        print(f"📸 استفاده از تصویر: {image_name}")

        # آپلود تصویر
        print("⬆️ آپلود تصویر...")
        upload_result = self.upload_image(image_path)

        if not upload_result['success']:
            print(f"❌ خطا در آپلود تصویر: {upload_result['error']}")
            return self.save_test_result(image_name, upload_result, None, None, None, None)

        print(f"✅ تصویر با موفقیت آپلود شد: {upload_result['path']}")

        # تولید داده‌های پروفایل
        profile_data = self.get_random_profile_data()
        print(f"👤 داده‌های پروفایل: {profile_data['full_name']}")

        # ایجاد پروفایل
        print("📝 ایجاد پروفایل...")
        profile_result = self.create_provider_profile(upload_result['path'], profile_data)

        # پاک‌سازی رکوردها (اختیاری)
        print("🧹 پاک‌سازی رکوردها...")
        cleanup_result = self.cleanup_via_api()
        if cleanup_result['success']:
            print(f"✅ {cleanup_result.get('message', 'پاک‌سازی انجام شد')}")
        else:
            print(f"❌ خطا در پاک‌سازی: {cleanup_result['error']}")

        # ذخیره نتیجه
        result = self.save_test_result(
            image_name,
            upload_result,
            profile_result,
            profile_data,
            cleanup_result,
            None  # user_id
        )

        if profile_result['success']:
            print("✅ تست با موفقیت انجام شد")
        else:
            print(f"❌ خطا در ایجاد پروفایل: {profile_result['error']}")

        return result

    def print_summary(self):
        """نمایش خلاصه نتایج"""
        if not self.results:
            print("📊 هیچ نتیجه‌ای برای نمایش وجود ندارد")
            return

        total_tests = len(self.results)
        successful_uploads = sum(1 for r in self.results if r.get('upload_result', {}).get('success', False))
        successful_profiles = sum(1 for r in self.results if r.get('profile_result', {}).get('success', False))
        successful_cleanups = sum(1 for r in self.results if r.get('cleanup_result', {}).get('success', False))

        print(f"\n📊 خلاصه نتایج:")
        print(f"🔢 مجموع تست‌ها: {total_tests}")
        print(f"⬆️ آپلودهای موفق: {successful_uploads}/{total_tests}")
        print(f"👤 پروفایل‌های موفق: {successful_profiles}/{total_tests}")
        print(f"🧹 پاک‌سازی‌های موفق: {successful_cleanups}/{total_tests}")

        # نمایش خطاهای رایج
        upload_errors = [r['upload_result'].get('error') for r in self.results
                        if not r.get('upload_result', {}).get('success', False)]
        profile_errors = [r['profile_result'].get('error') for r in self.results
                         if not r.get('profile_result', {}).get('success', False)]

        if upload_errors:
            print(f"\n❌ خطاهای آپلود:")
            for error in set(upload_errors):
                count = upload_errors.count(error)
                print(f"  • {error} ({count} بار)")

        if profile_errors:
            print(f"\n❌ خطاهای پروفایل:")
            for error in set(profile_errors):
                count = profile_errors.count(error)
                print(f"  • {error} ({count} بار)")

def main():
    """تابع اصلی"""
    script = AvatarDebugScript()

    print("🎯 اسکریپت دیباگ آواتار اپ میت")
    print("=" * 50)

    while True:
        print("\nگزینه‌های موجود:")
        print("1. اجرای یک تست")
        print("2. اجرای تست‌های مداوم")
        print("3. نمایش خلاصه نتایج")
        print("4. خروج")

        choice = input("\nانتخاب کنید (1-4): ").strip()

        if choice == '1':
            script.run_single_test()
        elif choice == '2':
            try:
                count_input = input("تعداد تست‌ها (Enter برای بی‌نهایت): ").strip()
                count = int(count_input) if count_input else None

                delay_input = input("تاخیر بین تست‌ها (ثانیه، پیش‌فرض 5): ").strip()
                delay = int(delay_input) if delay_input else 5

                script.run_continuous_tests(count, delay)
            except ValueError:
                print("❌ مقدار نامعتبر وارد شده")
        elif choice == '3':
            script.print_summary()
        elif choice == '4':
            print("👋 خداحافظ!")
            break
        else:
            print("❌ انتخاب نامعتبر")

if __name__ == "__main__":
    main()
