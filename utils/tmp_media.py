import os
import secrets
import shutil

from django.conf import settings
from django.core.files import File
from django.http import HttpRequest
from rest_framework import serializers, status
from rest_framework.generics import GenericAPIView
from rest_framework.parsers import <PERSON><PERSON>art<PERSON><PERSON><PERSON>, FormParser
from rest_framework.response import Response

from utils import absolute_url, file_location, guess_file_type
from utils import sizeof_fmt


class FileFieldSerializer(serializers.CharField):
    """
        a field to handled uploaded file
    """

    def get_rpath(self, p):
        # extract relative path of doc
        # تبدیل URL های media به static برای سازگاری
        if p.startswith('https://habibapp.com/media/'):
            p = p.replace('https://habibapp.com/media/', 'https://habibapp.com/static/')

        static_pos = p.find('/static/')
        if static_pos != -1:
            relative_path = p[static_pos + 8:]  # +8 برای رد کردن '/static/' (8 کاراکتر)
            # اگر path با /tmp/ شروع می‌شود، آن را برگردان
            if relative_path.startswith('tmp/'):
                return '/' + relative_path  # اضافه کردن / در ابتدا
            return relative_path

        # اگر /static/ پیدا نشد، احتمالاً relative path است
        return p

    def to_representation(self, value):
        if value:
            return absolute_url(self.context['request'], value.url)
        return ""

    def to_internal_value(self, data):
        if not data:
            return

        if "/tmp/" not in data:
            # value not changed and here we simply return old file path
            return self.get_rpath(data)

        if data.startswith('http'):
            data = self.get_rpath(data)

        fpath = file_location(data)
        if not os.path.exists(fpath):
            raise serializers.ValidationError("فایل آپلود شده وجود ندارد, لطفا دوباره فایل را آپلود کنید")

        return File(open(fpath, 'rb'), os.path.basename(data))


class UploadTmpSerializer(serializers.Serializer):
    file = serializers.FileField()

    url = serializers.URLField(read_only=True)
    name = serializers.CharField(read_only=True)
    size = serializers.CharField(read_only=True)
    mime_type = serializers.CharField(read_only=True)

    def to_representation(self, instance):
        data = super(UploadTmpSerializer, self).to_representation(instance)
        data['file'] = instance['file']
        return data

    def store_file(self, file):
        if settings.DEBUG:
            static_path = "static"
        else:
            static_path = "staticfiles"

        os.makedirs(f'{static_path}/tmp', exist_ok=True)
        fpath = f"/tmp/{secrets.token_urlsafe(4)}-{file.name}"
        shutil.move(file.temporary_file_path(), static_path + fpath)
        os.chmod(static_path + fpath, 0o644)

        return {
            'file': fpath,
            'url': absolute_url(self.context['request'], f"/static{fpath}"),
            'name': file.name,
            'size': sizeof_fmt(file.size),
            'mime_type': guess_file_type(fpath)
        }

    def validate(self, attrs):
        file_details = self.store_file(attrs['file'])
        return file_details


class UploadTmpMedia(GenericAPIView):
    """
        Files will remove every 1 hour
    """
    parser_classes = (FormParser, MultiPartParser)
    serializer_class = UploadTmpSerializer

    def post(self, request: HttpRequest, *args, **kwargs):
        serializer = UploadTmpSerializer(data=request.FILES, context={'request': request})
        is_valid = serializer.is_valid(raise_exception=True)
        if not is_valid:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.data)
