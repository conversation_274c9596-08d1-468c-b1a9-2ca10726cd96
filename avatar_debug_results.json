[{"timestamp": "2025-07-28T15:04:51.750798", "image_name": "علی-محمد-1717.png", "user_id": null, "upload_result": {"success": true, "path": "", "url": "", "response": {"success": true, "path": "https://habibapp.com/static/tmp/EEOW9g-%D8%B9%D9%84%DB%8C-%D9%85%D8%AD%D9%85%D8%AF-1717.png", "apath": "/tmp/EEOW9g-علی-محمد-1717.png", "name": "علی-محمد-1717.png", "size": "58.6 KB"}}, "profile_data": {"full_name": "دکتر فاطمه احمدی", "bio": "پژوهشگر تاریخ اسلام", "email": "<EMAIL>", "institution_name": "دانشگاه تهران", "wa_number": "989111222333", "birthdate": "1988-02-02", "social_medias": {"facebook": "https://facebook.com/997536", "x": "https://x.com/user6229", "instagram": "https://instagram.com/user2052", "youtube": "https://youtube.com/c/channel775"}, "languages": ["fa", "en"]}, "profile_result": {"success": false, "error": "HTTP 400: {\"avatar\":[\"This field may not be blank.\"]}", "response": "{\"avatar\":[\"This field may not be blank.\"]}", "status_code": 400}, "cleanup_result": {"success": true, "message": "پاک‌سازی از طریق API انجام نشد (نیاز به پیاده‌سازی API مخصوص)"}}, {"timestamp": "2025-07-28T15:06:12.121466", "image_name": "حسین-زین‌<PERSON><PERSON>عابدین-8421.png", "user_id": null, "upload_result": {"success": false, "error": "فیلد 'file' در response وجود ندارد: {'success': True, 'path': 'https://habibapp.com/static/tmp/9WDb8g-%D8%AD%D8%B3%DB%8C%D9%86-%D8%B2%DB%8C%D9%86%E2%80%8C%D8%A7%D9%84%D8%B9%D8%A7%D8%A8%D8%AF%DB%8C%D9%86-8421.png', 'apath': '/tmp/9WDb8g-حسین-زین\\u200cالعابدین-8421.png', 'name': 'حسین-زین\\u200cالعابدین-8421.png', 'size': '58.6 KB'}", "response": {"success": true, "path": "https://habibapp.com/static/tmp/9WDb8g-%D8%AD%D8%B3%DB%8C%D9%86-%D8%B2%DB%8C%D9%86%E2%80%8C%D8%A7%D9%84%D8%B9%D8%A7%D8%A8%D8%AF%DB%8C%D9%86-8421.png", "apath": "/tmp/9WDb8g-حسین-زین‌العابدین-8421.png", "name": "حسین-زین‌<PERSON><PERSON>عابدین-8421.png", "size": "58.6 KB"}}, "profile_data": null, "profile_result": null, "cleanup_result": null}]