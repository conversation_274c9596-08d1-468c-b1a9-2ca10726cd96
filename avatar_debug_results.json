[{"timestamp": "2025-07-28T15:04:51.750798", "image_name": "علی-محمد-1717.png", "user_id": null, "upload_result": {"success": true, "path": "", "url": "", "response": {"success": true, "path": "https://habibapp.com/static/tmp/EEOW9g-%D8%B9%D9%84%DB%8C-%D9%85%D8%AD%D9%85%D8%AF-1717.png", "apath": "/tmp/EEOW9g-علی-محمد-1717.png", "name": "علی-محمد-1717.png", "size": "58.6 KB"}}, "profile_data": {"full_name": "دکتر فاطمه احمدی", "bio": "پژوهشگر تاریخ اسلام", "email": "<EMAIL>", "institution_name": "دانشگاه تهران", "wa_number": "989111222333", "birthdate": "1988-02-02", "social_medias": {"facebook": "https://facebook.com/997536", "x": "https://x.com/user6229", "instagram": "https://instagram.com/user2052", "youtube": "https://youtube.com/c/channel775"}, "languages": ["fa", "en"]}, "profile_result": {"success": false, "error": "HTTP 400: {\"avatar\":[\"This field may not be blank.\"]}", "response": "{\"avatar\":[\"This field may not be blank.\"]}", "status_code": 400}, "cleanup_result": {"success": true, "message": "پاک‌سازی از طریق API انجام نشد (نیاز به پیاده‌سازی API مخصوص)"}}, {"timestamp": "2025-07-28T15:06:12.121466", "image_name": "حسین-زین‌<PERSON><PERSON>عابدین-8421.png", "user_id": null, "upload_result": {"success": false, "error": "فیلد 'file' در response وجود ندارد: {'success': True, 'path': 'https://habibapp.com/static/tmp/9WDb8g-%D8%AD%D8%B3%DB%8C%D9%86-%D8%B2%DB%8C%D9%86%E2%80%8C%D8%A7%D9%84%D8%B9%D8%A7%D8%A8%D8%AF%DB%8C%D9%86-8421.png', 'apath': '/tmp/9WDb8g-حسین-زین\\u200cالعابدین-8421.png', 'name': 'حسین-زین\\u200cالعابدین-8421.png', 'size': '58.6 KB'}", "response": {"success": true, "path": "https://habibapp.com/static/tmp/9WDb8g-%D8%AD%D8%B3%DB%8C%D9%86-%D8%B2%DB%8C%D9%86%E2%80%8C%D8%A7%D9%84%D8%B9%D8%A7%D8%A8%D8%AF%DB%8C%D9%86-8421.png", "apath": "/tmp/9WDb8g-حسین-زین‌العابدین-8421.png", "name": "حسین-زین‌<PERSON><PERSON>عابدین-8421.png", "size": "58.6 KB"}}, "profile_data": null, "profile_result": null, "cleanup_result": null}, {"timestamp": "2025-07-28T15:12:33.719333", "image_name": "khadijeh-kobra-4158.png", "upload_result": {"success": false, "error": "'file' field not found in response: {'success': True, 'path': 'https://habibapp.com/static/tmp/jTopsw-khadijeh-kobra-4158.png', 'apath': '/tmp/jTopsw-khadijeh-kobra-4158.png', 'name': 'khadijeh-kobra-4158.png', 'size': '58.6 KB'}", "response": {"success": true, "path": "https://habibapp.com/static/tmp/jTopsw-khadijeh-kobra-4158.png", "apath": "/tmp/jTopsw-khadijeh-kobra-4158.png", "name": "khadijeh-kobra-4158.png", "size": "58.6 KB"}}, "profile_result": null, "profile_data": null, "cleanup_result": null, "user_id": null}, {"timestamp": "2025-07-28T15:13:51.238864", "image_name": "zahra-batool-4500.png", "upload_result": {"success": true, "path": "https://habibapp.com/static/tmp/FYUVBQ-zahra-batool-4500.png", "url": "", "response": {"success": true, "path": "https://habibapp.com/static/tmp/FYUVBQ-zahra-batool-4500.png", "apath": "/tmp/FYUVBQ-zahra-batool-4500.png", "name": "zahra-batool-4500.png", "size": "58.6 KB"}}, "profile_result": {"success": false, "error": "HTTP 400: {\"avatar\":[\"فایل آپلود شده وجود ندارد, لطفا دوباره فایل را آپلود کنید\"]}", "response": "{\"avatar\":[\"فایل آپلود شده وجود ندارد, لطفا دوباره فایل را آپلود کنید\"]}", "status_code": 400}, "profile_data": {"full_name": "Dr. <PERSON><PERSON><PERSON>", "bio": "Islamic History Researcher", "email": "<EMAIL>", "institution_name": "Tehran University", "wa_number": "989111222333", "birthdate": "1989-12-02", "social_medias": {"facebook": "https://facebook.com/722158", "x": "https://x.com/user1037", "instagram": "https://instagram.com/user5351", "youtube": "https://youtube.com/c/channel238"}, "languages": ["fa", "en"]}, "cleanup_result": {"success": true, "message": "Cleanup via API not implemented (needs dedicated cleanup API)"}, "user_id": null}, {"timestamp": "2025-07-28T15:15:37.667874", "image_name": "fatemeh-masumeh-8247.png", "upload_result": {"success": true, "path": "/tmp/dBi6Sw-fatemeh-masumeh-8247.png", "url": "https://habibapp.com/static/tmp/dBi6Sw-fatemeh-masumeh-8247.png", "response": {"success": true, "path": "https://habibapp.com/static/tmp/dBi6Sw-fatemeh-masumeh-8247.png", "apath": "/tmp/dBi6Sw-fatemeh-masumeh-8247.png", "name": "fatemeh-masumeh-8247.png", "size": "58.6 KB"}}, "profile_result": {"success": true, "response": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "birthdate": "1987-11-08", "email": "ali.reza<PERSON>@example.com", "wa_number": "989123456789", "institution_name": "Qom University", "avatar": "https://habibapp.com/media/meet/providers/2025/07/dBi6Sw-fatemeh-masumeh-8247.png", "social_medias": {"facebook": "https://facebook.com/964294", "x": "https://x.com/user8621", "instagram": "https://instagram.com/user6871", "youtube": "https://youtube.com/c/channel378"}}, "status_code": 201}, "profile_data": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "email": "ali.reza<PERSON>@example.com", "institution_name": "Qom University", "wa_number": "989123456789", "birthdate": "1987-11-08", "social_medias": {"facebook": "https://facebook.com/964294", "x": "https://x.com/user8621", "instagram": "https://instagram.com/user6871", "youtube": "https://youtube.com/c/channel378"}, "languages": ["tr", "ur"]}, "cleanup_result": {"success": true, "message": "Cleanup via API not implemented (needs dedicated cleanup API)"}, "user_id": null}, {"timestamp": "2025-07-28T15:17:03.123242", "image_name": "hasan-mojtaba-7854.png", "upload_result": {"success": true, "path": "/tmp/cB9Y-w-hasan-mojtaba-7854.png", "url": "https://habibapp.com/static/tmp/cB9Y-w-hasan-mojtaba-7854.png", "response": {"success": true, "path": "https://habibapp.com/static/tmp/cB9Y-w-hasan-mojtaba-7854.png", "apath": "/tmp/cB9Y-w-hasan-mojtaba-7854.png", "name": "hasan-mojtaba-7854.png", "size": "58.6 KB"}}, "profile_result": {"success": true, "response": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "birthdate": "1987-11-08", "email": "ali.reza<PERSON>@example.com", "wa_number": "989123456789", "institution_name": "Qom University", "avatar": "https://habibapp.com/media/meet/providers/2025/07/dBi6Sw-fatemeh-masumeh-8247.png", "social_medias": {"x": "https://x.com/user8621", "youtube": "https://youtube.com/c/channel378", "facebook": "https://facebook.com/964294", "instagram": "https://instagram.com/user6871"}}, "status_code": 200}, "profile_data": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "email": "ali.reza<PERSON>@example.com", "institution_name": "Qom University", "wa_number": "989123456789", "birthdate": "1984-12-18", "social_medias": {"facebook": "https://facebook.com/985137", "x": "https://x.com/user5733", "instagram": "https://instagram.com/user6209", "youtube": "https://youtube.com/c/channel377"}, "languages": ["en", "ar"]}, "cleanup_result": {"success": true, "message": "Cleanup via API not implemented (needs dedicated cleanup API)"}, "user_id": null}, {"timestamp": "2025-07-28T15:17:08.086979", "image_name": "m<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-3343.png", "upload_result": {"success": true, "path": "/tmp/uBkd1g-moham<PERSON>-b<PERSON><PERSON>-3343.png", "url": "https://habibapp.com/static/tmp/uBkd1g-moham<PERSON>-b<PERSON>er-3343.png", "response": {"success": true, "path": "https://habibapp.com/static/tmp/uBkd1g-moham<PERSON>-b<PERSON>er-3343.png", "apath": "/tmp/uBkd1g-moham<PERSON>-b<PERSON><PERSON>-3343.png", "name": "m<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-3343.png", "size": "58.6 KB"}}, "profile_result": {"success": true, "response": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "birthdate": "1987-11-08", "email": "ali.reza<PERSON>@example.com", "wa_number": "989123456789", "institution_name": "Qom University", "avatar": "https://habibapp.com/media/meet/providers/2025/07/dBi6Sw-fatemeh-masumeh-8247.png", "social_medias": {"x": "https://x.com/user8621", "youtube": "https://youtube.com/c/channel378", "facebook": "https://facebook.com/964294", "instagram": "https://instagram.com/user6871"}}, "status_code": 200}, "profile_data": {"full_name": "Professor <PERSON>", "bio": "Islamic Ethics and Mysticism Teacher", "email": "m.ho<PERSON><PERSON>@example.com", "institution_name": "Najaf Seminary", "wa_number": "989987654321", "birthdate": "1976-05-27", "social_medias": {"facebook": "https://facebook.com/510192", "x": "https://x.com/user9031", "instagram": "https://instagram.com/user3236", "youtube": "https://youtube.com/c/channel500"}, "languages": ["tr", "ar"]}, "cleanup_result": {"success": true, "message": "Cleanup via API not implemented (needs dedicated cleanup API)"}, "user_id": null}, {"timestamp": "2025-07-28T15:17:10.810871", "image_name": "ali-naqi-9413.png", "upload_result": {"success": true, "path": "/tmp/9229Vg-ali-naqi-9413.png", "url": "https://habibapp.com/static/tmp/9229Vg-ali-naqi-9413.png", "response": {"success": true, "path": "https://habibapp.com/static/tmp/9229Vg-ali-naqi-9413.png", "apath": "/tmp/9229Vg-ali-naqi-9413.png", "name": "ali-naqi-9413.png", "size": "58.6 KB"}}, "profile_result": {"success": true, "response": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "birthdate": "1987-11-08", "email": "ali.reza<PERSON>@example.com", "wa_number": "989123456789", "institution_name": "Qom University", "avatar": "https://habibapp.com/media/meet/providers/2025/07/dBi6Sw-fatemeh-masumeh-8247.png", "social_medias": {"x": "https://x.com/user8621", "youtube": "https://youtube.com/c/channel378", "facebook": "https://facebook.com/964294", "instagram": "https://instagram.com/user6871"}}, "status_code": 200}, "profile_data": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "email": "ali.reza<PERSON>@example.com", "institution_name": "Qom University", "wa_number": "989123456789", "birthdate": "1977-06-25", "social_medias": {"facebook": "https://facebook.com/286277", "x": "https://x.com/user8964", "instagram": "https://instagram.com/user9585", "youtube": "https://youtube.com/c/channel434"}, "languages": ["en", "tr"]}, "cleanup_result": {"success": true, "message": "Cleanup via API not implemented (needs dedicated cleanup API)"}, "user_id": null}, {"timestamp": "2025-07-28T15:19:27.246955", "image_name": "hasan-ali-2863.png", "upload_result": {"success": true, "path": "/tmp/UiteFw-hasan-ali-2863.png", "url": "https://habibapp.com/static/tmp/UiteFw-hasan-ali-2863.png", "response": {"success": true, "path": "https://habibapp.com/static/tmp/UiteFw-hasan-ali-2863.png", "apath": "/tmp/UiteFw-hasan-ali-2863.png", "name": "hasan-ali-2863.png", "size": "58.6 KB"}}, "profile_result": {"success": true, "response": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "birthdate": "1987-11-08", "email": "ali.reza<PERSON>@example.com", "wa_number": "989123456789", "institution_name": "Qom University", "avatar": "https://habibapp.com/media/meet/providers/2025/07/dBi6Sw-fatemeh-masumeh-8247.png", "social_medias": {"x": "https://x.com/user8621", "youtube": "https://youtube.com/c/channel378", "facebook": "https://facebook.com/964294", "instagram": "https://instagram.com/user6871"}}, "status_code": 200}, "profile_data": {"full_name": "Professor <PERSON>", "bio": "Islamic Ethics and Mysticism Teacher", "email": "m.ho<PERSON><PERSON>@example.com", "institution_name": "Najaf Seminary", "wa_number": "989987654321", "birthdate": "1989-08-20", "social_medias": {"facebook": "https://facebook.com/920255", "x": "https://x.com/user3703", "instagram": "https://instagram.com/user2543", "youtube": "https://youtube.com/c/channel691"}, "languages": ["tr", "en"]}, "cleanup_result": {"success": true, "message": "Cleanup via API not implemented (needs dedicated cleanup API)"}, "user_id": null}, {"timestamp": "2025-07-28T15:24:32.101186", "image_name": "zeinab-kobra-3508.png", "upload_result": {"success": true, "path": "/tmp/uT0mrA-zeinab-kobra-3508.png", "url": "https://habibapp.com/static/tmp/uT0mrA-zeinab-kobra-3508.png", "response": {"success": true, "path": "https://habibapp.com/static/tmp/uT0mrA-zeinab-kobra-3508.png", "apath": "/tmp/uT0mrA-zeinab-kobra-3508.png", "name": "zeinab-kobra-3508.png", "size": "58.6 KB"}}, "profile_result": {"success": true, "response": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "birthdate": "1987-11-08", "email": "ali.reza<PERSON>@example.com", "wa_number": "989123456789", "institution_name": "Qom University", "avatar": "https://habibapp.com/media/meet/providers/2025/07/dBi6Sw-fatemeh-masumeh-8247.png", "social_medias": {"x": "https://x.com/user8621", "youtube": "https://youtube.com/c/channel378", "facebook": "https://facebook.com/964294", "instagram": "https://instagram.com/user6871"}}, "status_code": 200}, "profile_data": {"full_name": "Dr. <PERSON>", "bio": "Quran Sciences and Tafsir Specialist", "email": "ali.reza<PERSON>@example.com", "institution_name": "Qom University", "wa_number": "989123456789", "birthdate": "1989-11-03", "social_medias": {"facebook": "https://facebook.com/418832", "x": "https://x.com/user2576", "instagram": "https://instagram.com/user2325", "youtube": "https://youtube.com/c/channel859"}, "languages": ["fa", "ur"]}, "cleanup_result": {"success": true, "message": "Cleanup via API not implemented (needs dedicated cleanup API)"}, "user_id": null}]